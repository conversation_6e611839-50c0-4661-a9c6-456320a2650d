import sys
import json
import requests
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QWidget, QHBoxLayout, QVBoxLayout,
                             QLabel, QPushButton, QGridLayout, QFrame,
                             QSpacerItem, QSizePolicy, QGraphicsDropShadowEffect,
                             QMessageBox, QScrollArea)
from PyQt5.QtGui import QFont, QPixmap, QPainter, QBitmap, QColor
from PyQt5.QtCore import Qt, QSize, QRect, QTimer
from Mastaji import MainWindow

def create_avatar_pixmap(size):
        """创建橙色圆形背景和白色字母'M'的头像"""
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.transparent)
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        # 背景
        painter.setBrush(QColor("#f9a825")) # 橙色
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(0, 0, size, size)
        # 字母
        painter.setPen(QColor("white"))
        font = QFont("Arial", size // 2)
        font.setBold(True)
        painter.setFont(font)
        painter.drawText(QRect(0, 0, size, size), Qt.AlignCenter, "M")
        painter.end()
        return pixmap

class Dashboard(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle(" ")
        self.setGeometry(100, 100, 960, 600)
        self.setStyleSheet("background-color: #FFFFFF;")

        # 教师信息 - 将从后端获取
        self.teacher_info = {}
        self.teacher_name = '教师'

        # 加载配置
        self.load_config()

        # 窗口居中
        self.center_window()

        # 课程数据
        self.courses = []

        # 定时器用于更新时间和刷新课程
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time_and_greeting)
        self.timer.start(60000)  # 每分钟更新一次

        # 课程刷新定时器
        self.course_timer = QTimer()
        self.course_timer.timeout.connect(self.load_courses)
        self.course_timer.start(30000)  # 每30秒刷新一次课程

        # 主水平布局
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建并添加左侧边栏
        left_sidebar = self._create_left_sidebar()
        main_layout.addWidget(left_sidebar)

        # 创建并添加右侧主面板
        right_pane = self._create_right_pane()
        main_layout.addWidget(right_pane)

        self.setLayout(main_layout)

        # 初始加载教师信息和课程
        self.load_teacher_info_and_courses()

    def center_window(self):
        """窗口居中显示"""
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()
        window_geometry = self.frameGeometry()
        center_point = screen_geometry.center()
        window_geometry.moveCenter(center_point)
        self.move(window_geometry.topLeft())

    def load_config(self):
        """加载配置文件"""
        try:
            import os
            config_path = os.path.join(os.path.dirname(__file__), 'config.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")

    def load_teacher_info_and_courses(self):
        """从后端获取教师信息并加载正在进行的课程"""
        try:
            # 创建session来保持登录状态
            session = requests.Session()

            # 先登录获取session
            login_data = {
                'user_id': self.config.get('saved_login', {}).get('user_id', 'T001'),
                'password': self.config.get('saved_login', {}).get('password', '123')
            }

            login_response = session.post(
                f"{self.config['server']['url']}/login",
                data=login_data,
                timeout=self.config['server']['timeout']
            )

            if login_response.status_code == 200:
                # 获取教师信息
                self.teacher_info = {
                    'teacher_id': login_data['user_id'],
                    'user_id': login_data['user_id'],
                    'session': session,
                    'server_url': self.config['server']['url']
                }

                # 从session中获取教师姓名（登录后session会包含教师信息）

                # 获取课程安排
                response = session.get(
                    f"{self.config['server']['url']}/teacher/get_course_schedules",
                    timeout=self.config['server']['timeout']
                )

                if response.status_code == 200:
                    data = response.json()
                    if data.get('status') == 'success':
                        # 筛选正在进行的课程
                        all_courses = data.get('course_schedules', [])
                        self.courses = [course for course in all_courses if course.get('status') == 'in_progress']

                        # 从课程数据中获取教师姓名（如果有的话）
                        if all_courses:
                            # 假设教师姓名可以从配置或其他方式获取
                            self.teacher_name = self.config.get('saved_login', {}).get('teacher_name', '教师')

                        self.update_teacher_display()
                        self.update_course_display()
                    else:
                        print(f"获取课程失败: {data.get('message', '未知错误')}")
                        self.courses = []
                        self.update_course_display()
                else:
                    print(f"获取课程失败，状态码: {response.status_code}")
                    self.courses = []
                    self.update_course_display()
            else:
                print(f"登录失败，状态码: {login_response.status_code}")
                self.courses = []
                self.update_course_display()

        except Exception as e:
            print(f"连接后端失败: {e}")
            self.courses = []
            self.update_course_display()

    def load_courses(self):
        """刷新课程数据（定时器调用）"""
        self.load_teacher_info_and_courses()

    def update_teacher_display(self):
        """更新教师信息显示"""
        if hasattr(self, 'name_label'):
            self.name_label.setText(self.teacher_name)

    def update_time_and_greeting(self):
        """更新时间和问候语"""
        now = datetime.now()
        hour = now.hour

        # 根据时间确定问候语
        if 5 <= hour < 12:
            greeting = "上午好!"
        elif 12 <= hour < 18:
            greeting = "下午好!"
        else:
            greeting = "晚上好!"

        # 更新显示
        if hasattr(self, 'greeting_label'):
            self.greeting_label.setText(greeting)
        if hasattr(self, 'date_label'):
            date_str = now.strftime("%Y年%m月%d日 %A")
            # 转换英文星期为中文
            weekdays = {
                'Monday': '星期一', 'Tuesday': '星期二', 'Wednesday': '星期三',
                'Thursday': '星期四', 'Friday': '星期五', 'Saturday': '星期六', 'Sunday': '星期日'
            }
            for en, zh in weekdays.items():
                date_str = date_str.replace(en, zh)
            self.date_label.setText(date_str)

    def _create_left_sidebar(self):
        """创建左侧边栏"""
        sidebar_widget = QWidget()
        sidebar_widget.setFixedWidth(240)
        sidebar_widget.setStyleSheet("background-color: #f0f2f5;")

        sidebar_layout = QVBoxLayout(sidebar_widget)
        sidebar_layout.setContentsMargins(20, 20, 20, 10)
        sidebar_layout.setSpacing(15)
        sidebar_layout.setAlignment(Qt.AlignTop)

        # 头像
        avatar_label = QLabel()
        raw_pixmap = create_avatar_pixmap(80)
        
        # --- 创建圆形头像 ---
        circular_pixmap = QPixmap(raw_pixmap.size())
        circular_pixmap.fill(Qt.transparent)
        mask_painter = QPainter(circular_pixmap)
        mask_painter.setRenderHint(QPainter.Antialiasing)
        mask_painter.setBrush(Qt.black)
        mask_painter.drawEllipse(0, 0, raw_pixmap.width(), raw_pixmap.height())
        mask_painter.end()
        
        raw_pixmap.setMask(circular_pixmap.mask())
        avatar_label.setPixmap(raw_pixmap)
        avatar_label.setAlignment(Qt.AlignCenter)
        
        # 用户名
        self.name_label = QLabel(self.teacher_name)
        self.name_label.setFont(QFont("微软雅黑", 14))
        self.name_label.setAlignment(Qt.AlignCenter)

        sidebar_layout.addWidget(avatar_label)
        sidebar_layout.addWidget(self.name_label)
        sidebar_layout.addSpacing(20)

        # 功能按钮网格
        grid_layout = QGridLayout()
        grid_layout.setSpacing(15)

        buttons_info = [
            {"text": "上课", "text_icon": "📚", "color": "#4a90e2"},
        ]

        positions = [(i, j) for i in range(2) for j in range(2)]

        for position, info in zip(positions, buttons_info):
            button = QPushButton(f" {info['text']}")
            button.setText(f"{info['text_icon']} {info['text']}")  # 使用文字图标代替
            button.setIconSize(QSize(20, 20))  # 文字图标大小
            button.setFont(QFont("微软雅黑", 11))
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {info['color']};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 15px;
                    text-align: center;
                }}
                QPushButton:hover {{
                    background-color: {QColor(info['color']).lighter(115).name()};
                }}
            """)
            grid_layout.addWidget(button, *position)
            
        sidebar_layout.addLayout(grid_layout)

        # 弹簧, 将底部内容推到底部
        sidebar_layout.addSpacerItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # 底部工具
        bottom_layout = QHBoxLayout()
        bottom_layout.setContentsMargins(0,0,0,0)

        bottom_layout.addStretch()
        
        sidebar_layout.addLayout(bottom_layout)
        
        return sidebar_widget

    def _create_right_pane(self):
        """创建右侧主面板"""
        pane_widget = QWidget()
        pane_layout = QVBoxLayout(pane_widget)
        pane_layout.setContentsMargins(40, 25, 40, 40)
        pane_layout.setSpacing(20)
        pane_layout.setAlignment(Qt.AlignTop)

        # 顶部栏：问候语和图标
        top_bar_layout = QHBoxLayout()
        top_bar_layout.setSpacing(10)

        # 问候语和日期
        greeting_layout = QVBoxLayout()
        greeting_layout.setSpacing(5)

        # 获取当前时间和问候语
        now = datetime.now()
        hour = now.hour
        if 5 <= hour < 12:
            greeting_text = "上午好!"
        elif 12 <= hour < 18:
            greeting_text = "下午好!"
        else:
            greeting_text = "晚上好!"

        self.greeting_label = QLabel(greeting_text)
        self.greeting_label.setFont(QFont("微软雅黑", 24, QFont.Bold))
        self.greeting_label.setStyleSheet("color: #333;")

        # 格式化日期
        date_str = now.strftime("%Y年%m月%d日 %A")
        weekdays = {
            'Monday': '星期一', 'Tuesday': '星期二', 'Wednesday': '星期三',
            'Thursday': '星期四', 'Friday': '星期五', 'Saturday': '星期六', 'Sunday': '星期日'
        }
        for en, zh in weekdays.items():
            date_str = date_str.replace(en, zh)

        self.date_label = QLabel(date_str)
        self.date_label.setFont(QFont("微软雅黑", 11))
        self.date_label.setStyleSheet("color: #888;")

        greeting_layout.addWidget(self.greeting_label)
        greeting_layout.addWidget(self.date_label)
        
        top_bar_layout.addLayout(greeting_layout)
        top_bar_layout.addStretch()

        # 右上角图标
        new_badge = QLabel("NEW")
        new_badge.setFont(QFont("Arial", 8, QFont.Bold))
        new_badge.setStyleSheet("""
            background-color: #f56c6c; 
            color: white; 
            padding: 2px 4px; 
            border-radius: 3px;
        """)
        new_badge.setFixedHeight(16)
        
        settings_btn = QPushButton()
        settings_btn.setText('⚙️')  # 使用齿轮emoji作为设置图标
        settings_btn.setStyleSheet("border:none;")
        settings_btn.setCursor(Qt.PointingHandCursor)
        
        help_btn = QPushButton()
        help_btn.setText('❓')  # 使用问号emoji作为帮助图标
        help_btn.setStyleSheet("border:none;")
        help_btn.setCursor(Qt.PointingHandCursor)
        
        top_bar_layout.addWidget(new_badge, alignment=Qt.AlignTop)
        top_bar_layout.addWidget(settings_btn, alignment=Qt.AlignTop)
        top_bar_layout.addWidget(help_btn, alignment=Qt.AlignTop)
        
        # 刷新按钮，稍微往下一点
        refresh_layout = QHBoxLayout()
        refresh_layout.addStretch()
        refresh_btn = QPushButton()
        refresh_btn.setText('🔄')  # 使用循环箭头emoji作为刷新图标
        refresh_btn.setStyleSheet("border:none;")
        refresh_btn.setCursor(Qt.PointingHandCursor)
        refresh_layout.addWidget(refresh_btn)
        
        pane_layout.addLayout(top_bar_layout)
        pane_layout.addLayout(refresh_layout)
        pane_layout.addSpacing(20)

        # 正在上的课
        in_progress_layout = QHBoxLayout()
        in_progress_layout.setSpacing(10)
        
        # 动画图标可以用QMovie和GIF实现，这里用静态图标简化
        sound_wave_label = QLabel()
        sound_wave_label.setText('📊')  # 使用图表emoji作为声音波形图标
        
        in_progress_label = QLabel("正在上的课")
        in_progress_label.setFont(QFont("微软雅黑", 11, QFont.Bold))
        in_progress_label.setStyleSheet("color: #333;")
        
        in_progress_layout.addWidget(sound_wave_label)
        in_progress_layout.addWidget(in_progress_label)
        in_progress_layout.addStretch()
        
        pane_layout.addLayout(in_progress_layout)

        # 课程显示区域（使用滚动区域）
        self.course_scroll_area = QScrollArea()
        self.course_scroll_area.setWidgetResizable(True)
        self.course_scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f0f0f0;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background-color: #c0c0c0;
                border-radius: 4px;
            }
        """)

        self.course_widget = QWidget()
        self.course_layout = QVBoxLayout(self.course_widget)
        self.course_layout.setSpacing(15)
        self.course_layout.setContentsMargins(0, 0, 0, 0)

        self.course_scroll_area.setWidget(self.course_widget)
        pane_layout.addWidget(self.course_scroll_area)

        # 弹簧
        pane_layout.addStretch()

        return pane_widget

    def update_course_display(self):
        """更新课程显示"""
        # 清除现有的课程卡片
        for i in reversed(range(self.course_layout.count())):
            child = self.course_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        if not self.courses:
            # 没有正在进行的课程时显示提示
            no_course_label = QLabel("当前没有正在进行的课程")
            no_course_label.setFont(QFont("微软雅黑", 16))
            no_course_label.setStyleSheet("""
                color: #888;
                padding: 40px;
                text-align: center;
                background-color: #f8f9fa;
                border-radius: 12px;
                border: 2px dashed #ddd;
            """)
            no_course_label.setAlignment(Qt.AlignCenter)
            self.course_layout.addWidget(no_course_label)
        else:
            # 显示正在进行的课程
            for course in self.courses:
                course_card = self._create_class_card(course)
                self.course_layout.addWidget(course_card)

    def _create_class_card(self, course_data=None):
        """创建课程卡片"""
        card_widget = QWidget()
        card_widget.setMinimumHeight(100)
        card_widget.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border-radius: 12px;
            }
        """)
        
        card_layout = QHBoxLayout(card_widget)
        card_layout.setContentsMargins(20, 20, 20, 20)
        card_layout.setSpacing(15)
        
        # 中间课程信息
        info_layout = QVBoxLayout()
        info_layout.setSpacing(5)
        info_layout.setContentsMargins(0,0,0,0)
    

        course_name = QLabel(course_data.get('course_name', '未知课程'))
        course_name.setFont(QFont("微软雅黑", 14, QFont.Bold))

        # 组合显示班级和教室信息
        class_info = course_data.get('class_name', '未知班级')
        classroom_info = course_data.get('classroom_name', '')
        if classroom_info:
            class_info += f" | {classroom_info}"

        class_name = QLabel(class_info)
        class_name.setFont(QFont("微软雅黑", 10))
        class_name.setStyleSheet("color: #666;")

        # 添加时间信息
        time_info = f"{course_data.get('start_time', '')} - {course_data.get('end_time', '')}"
        if time_info.strip() != ' - ':
            time_label = QLabel(time_info)
            time_label.setFont(QFont("微软雅黑", 9))
            time_label.setStyleSheet("color: #999;")
            info_layout.addWidget(time_label)
        
        info_layout.addWidget(course_name)
        info_layout.addWidget(class_name)
        
        # 右侧进入课堂按钮
        enter_btn = QPushButton("进入课堂")
        enter_btn.setFont(QFont("微软雅黑", 11))
        enter_btn.setCursor(Qt.PointingHandCursor)
        enter_btn.setFixedSize(120, 40)
        enter_btn.setStyleSheet("""
            QPushButton {
                background-color: #e3f2fd;
                color: #4a90e2;
                border: none;
                border-radius: 20px;
            }
            QPushButton:hover {
                background-color: #d0e8fb;
            }
        """)

        # 绑定点击事件
        course_id = course_data.get('id')
        if course_id:
            enter_btn.clicked.connect(lambda: self.enter_classroom(course_id))

        card_layout.addLayout(info_layout)
        card_layout.addStretch()
        card_layout.addWidget(enter_btn, alignment=Qt.AlignCenter)
        
        # 卡片阴影
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(25)
        shadow.setXOffset(0)
        shadow.setYOffset(5)
        shadow.setColor(QColor(0, 0, 0, 30))
        card_widget.setGraphicsEffect(shadow)
        
        return card_widget

    def enter_classroom(self, course_id):
        """进入课堂"""
        try:
            # 关闭当前dashboard窗口
            self.close()

            # 启动Mastaji主程序，传递课程ID
            mastaji_window = MainWindow(teacher_info=self.teacher_info, current_course_id=course_id)
            mastaji_window.show()

        except Exception as e:
            QMessageBox.warning(self, '错误', f'进入课堂失败: {str(e)}')


if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = Dashboard()
    window.show()
    sys.exit(app.exec_())