#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的功能
验证从后端获取teacher_info和select_people功能的修改
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dashboard_import():
    """测试dashboard模块导入"""
    try:
        from dashboard import Dashboard
        print("✅ Dashboard 模块导入成功")
        return True
    except Exception as e:
        print(f"❌ Dashboard 模块导入失败: {e}")
        return False

def test_select_people_import():
    """测试Select_People模块导入"""
    try:
        from Select_People import RandomStudentPicker
        print("✅ RandomStudentPicker 模块导入成功")
        return True
    except Exception as e:
        print(f"❌ RandomStudentPicker 模块导入失败: {e}")
        return False

def test_mastaji_import():
    """测试Mastaji模块导入"""
    try:
        from Mastaji import MainWindow
        print("✅ MainWindow 模块导入成功")
        return True
    except Exception as e:
        print(f"❌ MainWindow 模块导入失败: {e}")
        return False

def test_dashboard_creation():
    """测试Dashboard创建"""
    try:
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from dashboard import Dashboard
        dashboard = Dashboard()
        print("✅ Dashboard 创建成功")
        print(f"   - 教师信息: {dashboard.teacher_info}")
        print(f"   - 教师姓名: {dashboard.teacher_name}")
        return True
    except Exception as e:
        print(f"❌ Dashboard 创建失败: {e}")
        return False

def test_select_people_creation():
    """测试RandomStudentPicker创建"""
    try:
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from Select_People import RandomStudentPicker
        
        # 测试无参数创建
        picker1 = RandomStudentPicker()
        print("✅ RandomStudentPicker (无参数) 创建成功")
        
        # 测试带课程ID创建
        picker2 = RandomStudentPicker(current_course_id="test_course_123")
        print("✅ RandomStudentPicker (带课程ID) 创建成功")
        print(f"   - 当前课程ID: {picker2.current_course_id}")
        
        return True
    except Exception as e:
        print(f"❌ RandomStudentPicker 创建失败: {e}")
        return False

def test_mainwindow_creation():
    """测试MainWindow创建"""
    try:
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from Mastaji import MainWindow
        
        # 测试无参数创建
        main1 = MainWindow()
        print("✅ MainWindow (无参数) 创建成功")
        
        # 测试带课程ID创建
        teacher_info = {'user_id': 'T001', 'name': '测试教师'}
        main2 = MainWindow(teacher_info=teacher_info, current_course_id="test_course_123")
        print("✅ MainWindow (带参数) 创建成功")
        print(f"   - 教师信息: {main2.teacher_info}")
        print(f"   - 当前课程ID: {main2.current_course_id}")
        
        return True
    except Exception as e:
        print(f"❌ MainWindow 创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("MASTAJI_A 修改功能测试")
    print("=" * 60)
    
    tests = [
        ("模块导入测试", [
            test_dashboard_import,
            test_select_people_import,
            test_mastaji_import
        ]),
        ("对象创建测试", [
            test_dashboard_creation,
            test_select_people_creation,
            test_mainwindow_creation
        ])
    ]
    
    total_tests = 0
    passed_tests = 0
    
    for category, test_functions in tests:
        print(f"\n{category}:")
        print("-" * 40)
        
        for test_func in test_functions:
            total_tests += 1
            if test_func():
                passed_tests += 1
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！修改成功！")
        print("\n主要修改内容:")
        print("1. ✅ Dashboard 从后端获取教师信息，不再手动指定")
        print("2. ✅ Select_People 移除本地JSON数据备选功能")
        print("3. ✅ Select_People 支持直接传入课程ID，只获取本堂课数据")
        print("4. ✅ MainWindow 支持传递课程ID给Select_People")
        print("5. ✅ 界面适配：有课程ID时隐藏课程选择器")
    else:
        print("❌ 部分测试失败，请检查代码")
    
    return passed_tests == total_tests

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
