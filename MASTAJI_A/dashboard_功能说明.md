# Dashboard 界面完善功能说明

## 已完成的功能

### 1. 时间和问候语显示
- ✅ 根据当前时间自动显示问候语（上午好/下午好/晚上好）
- ✅ 实时显示当前日期（年月日 星期）
- ✅ 每分钟自动更新时间显示

### 2. 教师信息显示
- ✅ 在左侧边栏显示教师姓名
- ✅ 支持通过构造函数传入教师信息
- ✅ 显示教师头像（圆形橙色背景）

### 3. 后端课程数据获取
- ✅ 连接MASTAJI_S后端服务
- ✅ 自动登录获取session
- ✅ 获取当前教师的所有课程安排
- ✅ 筛选status为'in_progress'的正在进行课程
- ✅ 每30秒自动刷新课程数据

### 4. 课程显示界面
- ✅ 动态显示正在进行的课程卡片
- ✅ 课程卡片包含：
  - 课程名称（如：大学英语）
  - 班级信息（如：计算机2101班）
  - 教室信息（如：教室101）
  - 上课时间（如：08:00 - 10:00）
- ✅ 支持滚动显示多个课程
- ✅ 卡片阴影效果和悬停效果

### 5. 无课程提示
- ✅ 当没有正在进行的课程时显示友好提示
- ✅ 虚线边框的提示框样式
- ✅ 居中显示"当前没有正在进行的课程"

### 6. 进入课堂功能
- ✅ 每个课程卡片都有"进入课堂"按钮
- ✅ 点击按钮关闭dashboard窗口
- ✅ 启动Mastaji主程序（上课界面）
- ✅ 传递教师信息到主程序

## 技术实现

### 后端API集成
- 使用requests库进行HTTP通信
- 自动处理登录session管理
- 错误处理和重试机制

### 界面设计
- PyQt5实现现代化界面
- 响应式布局设计
- 自定义样式表美化

### 数据处理
- JSON数据解析
- 实时数据更新
- 定时器管理

## 测试验证

### 后端连接测试
- ✅ 成功连接到http://localhost:8080
- ✅ 登录验证通过（T001/123）
- ✅ 获取课程数据正常

### 课程数据测试
- ✅ 发现1个正在进行的课程
- ✅ 课程信息：大学英语 - 计算机2101班
- ✅ 时间信息：08:00 - 10:00

### 界面功能测试
- ✅ Dashboard窗口正常启动
- ✅ 教师信息正确显示
- ✅ 时间问候语正常更新
- ✅ 课程卡片正确渲染

## 使用方法

```python
# 启动dashboard
from dashboard import Dashboard

teacher_info = {
    'name': '张老师',
    'teacher_id': 'T001'
}

app = QApplication(sys.argv)
window = Dashboard(teacher_info=teacher_info)
window.show()
sys.exit(app.exec_())
```

## 配置要求

1. 后端服务MASTAJI_S必须运行在http://localhost:8080
2. 教师账号T001/123必须存在且有权限
3. 数据库中必须有status为'in_progress'的课程记录

## 文件结构

```
MASTAJI_A/
├── dashboard.py          # 主dashboard界面
├── test_dashboard.py     # 测试启动脚本
├── test_api.py          # API测试脚本
├── config.json          # 配置文件
└── Mastaji.py           # 主上课程序
```
