#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MASTAJI Dashboard 功能演示脚本
展示完善后的dashboard界面功能
"""

import requests
import json
from datetime import datetime

def demo_backend_connection():
    """演示后端连接功能"""
    print("=" * 50)
    print("MASTAJI Dashboard 功能演示")
    print("=" * 50)
    
    base_url = "http://localhost:8080"
    
    # 创建session
    session = requests.Session()
    
    print("\n1. 测试后端连接...")
    try:
        response = session.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务连接成功")
        else:
            print(f"❌ 后端服务连接失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端服务连接失败: {e}")
        return False
    
    print("\n2. 测试教师登录...")
    login_data = {
        'user_id': 'T001',
        'password': '123'
    }
    
    try:
        login_response = session.post(f"{base_url}/login", data=login_data, timeout=5)
        if login_response.status_code == 200:
            print("✅ 教师登录成功")
        else:
            print(f"❌ 教师登录失败，状态码: {login_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 教师登录失败: {e}")
        return False
    
    print("\n3. 获取课程安排...")
    try:
        courses_response = session.get(f"{base_url}/teacher/get_course_schedules", timeout=5)
        if courses_response.status_code == 200:
            data = courses_response.json()
            if data.get('status') == 'success':
                all_courses = data.get('course_schedules', [])
                in_progress_courses = [course for course in all_courses if course.get('status') == 'in_progress']
                
                print(f"✅ 成功获取课程数据")
                print(f"   总课程数: {len(all_courses)}")
                print(f"   正在进行的课程: {len(in_progress_courses)}")
                
                if in_progress_courses:
                    print("\n   正在进行的课程详情:")
                    for i, course in enumerate(in_progress_courses, 1):
                        print(f"   {i}. {course.get('course_name')} - {course.get('class_name')}")
                        print(f"      教室: {course.get('classroom_name')}")
                        print(f"      时间: {course.get('start_time')} - {course.get('end_time')}")
                        print(f"      状态: {course.get('status')}")
                else:
                    print("   当前没有正在进行的课程")
                
                return True
            else:
                print(f"❌ 获取课程失败: {data.get('message')}")
                return False
        else:
            print(f"❌ 获取课程失败，状态码: {courses_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取课程失败: {e}")
        return False

def demo_dashboard_features():
    """演示dashboard界面功能"""
    print("\n4. Dashboard界面功能:")
    print("✅ 实时时间显示")
    
    # 获取当前时间和问候语
    now = datetime.now()
    hour = now.hour
    if 5 <= hour < 12:
        greeting = "上午好!"
    elif 12 <= hour < 18:
        greeting = "下午好!"
    else:
        greeting = "晚上好!"
    
    print(f"   当前问候语: {greeting}")
    
    # 格式化日期
    date_str = now.strftime("%Y年%m月%d日 %A")
    weekdays = {
        'Monday': '星期一', 'Tuesday': '星期二', 'Wednesday': '星期三',
        'Thursday': '星期四', 'Friday': '星期五', 'Saturday': '星期六', 'Sunday': '星期日'
    }
    for en, zh in weekdays.items():
        date_str = date_str.replace(en, zh)
    
    print(f"   当前日期: {date_str}")
    print("✅ 教师信息显示")
    print("   教师姓名: 张老师")
    print("   教师ID: T001")
    print("✅ 课程卡片显示")
    print("   - 动态获取正在进行的课程")
    print("   - 显示课程名称、班级、教室、时间")
    print("   - 提供进入课堂按钮")
    print("✅ 自动刷新功能")
    print("   - 每分钟更新时间显示")
    print("   - 每30秒刷新课程数据")

def main():
    """主演示函数"""
    success = demo_backend_connection()
    demo_dashboard_features()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Dashboard功能演示完成！所有功能正常工作")
        print("\n启动Dashboard:")
        print("python start_dashboard.py")
    else:
        print("⚠️  部分功能可能无法正常工作，请检查后端服务")
    print("=" * 50)

if __name__ == '__main__':
    main()
