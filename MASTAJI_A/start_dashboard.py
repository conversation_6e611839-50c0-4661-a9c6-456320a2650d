#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MASTAJI 教学管理系统 - Dashboard启动脚本
完善的dashboard界面，包括时间、问候语、教师姓名、课程获取等功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMessageBox
from dashboard import Dashboard

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("MASTAJI 教学管理系统")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("MASTAJI")
    
    try:
        # 教师信息将从后端获取，不再手动指定

        # 创建并显示dashboard
        window = Dashboard()
        window.show()
        
        print("Dashboard启动成功！")
        print("功能特性：")
        print("- 实时时间和问候语显示")
        print("- 教师信息展示")
        print("- 自动获取正在进行的课程")
        print("- 点击课程卡片进入上课界面")
        print("- 每30秒自动刷新课程数据")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        QMessageBox.critical(None, "启动错误", f"Dashboard启动失败：{str(e)}")
        print(f"启动失败：{e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
